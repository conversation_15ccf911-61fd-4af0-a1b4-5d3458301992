-- 问卷系统数据库初始化脚本
CREATE DATABASE IF NOT EXISTS yzjudge DEFAULT CHARACTER SET = utf8mb4;

USE yzjudge;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===========================
-- 用户管理表 (User Management)
-- ===========================

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL UNIQUE COMMENT '用户名 (唯一)',
    `password` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
    `role` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 学生用户表
CREATE TABLE IF NOT EXISTS `student_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '学生ID',
    `name` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
    `student_number` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL UNIQUE COMMENT '学号 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_student_number` (`student_number`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生用户表';

-- 社会人士用户表
CREATE TABLE IF NOT EXISTS `other_users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '社会人士ID',
    `phone` VARCHAR(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL UNIQUE COMMENT '手机号 (唯一)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社会人士用户表';


-- ===========================
-- 问卷与内容表 (Questionnaire & Content)
-- ===========================

-- 问卷表
CREATE TABLE IF NOT EXISTS `questionnaires` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '问卷ID',
    `title` VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
    `target_audience` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标受众',
    `status` INT NOT NULL DEFAULT 0 COMMENT '状态 (0:草稿 1:发布 2:关闭)',
    `creator_id` BIGINT NOT NULL COMMENT '创建者ID',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_creator_id` (`creator_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问卷表';

-- 问卷页面表
CREATE TABLE IF NOT EXISTS `questionnaire_pages` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '页面ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '所属问卷ID',
    `page_number` INT NOT NULL COMMENT '页码',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_questionnaire_id` (`questionnaire_id`),
    INDEX `idx_page_number` (`page_number`),
    UNIQUE KEY `uk_questionnaire_page` (`questionnaire_id`, `page_number`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问卷页面表';

-- 题目表
CREATE TABLE IF NOT EXISTS `questions` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '题目ID',
    `page_id` BIGINT NOT NULL COMMENT '所属页面ID',
    `content_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题干类型 (TEXT/IMAGE)',
    `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题干内容',
    `display_order` INT NOT NULL COMMENT '显示顺序',
    `correct_answer_option_id` BIGINT NULL COMMENT '正确答案ID (nullable)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_page_id` (`page_id`),
    INDEX `idx_display_order` (`display_order`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目表';

-- 题目选项表
CREATE TABLE IF NOT EXISTS `question_options` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '选项ID',
    `question_id` BIGINT NOT NULL COMMENT '所属题目ID',
    `option_type` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '选项类型 (TEXT/IMAGE)',
    `content` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容 (文本/URL)',
    `option_code` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '选项代号',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_question_id` (`question_id`),
    INDEX `idx_option_code` (`option_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目选项表';

-- ===========================
-- 答题与结果表 (Answer & Results)
-- ===========================

-- 答卷表
CREATE TABLE IF NOT EXISTS `answer_sheets` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答卷ID',
    `questionnaire_id` BIGINT NOT NULL COMMENT '所属问卷ID',
    `submitter_type` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提交者类型 (STUDENT/OTHER)',
    `student_user_id` BIGINT NULL COMMENT '学生ID (nullable)',
    `other_user_id` BIGINT NULL COMMENT '社会人士ID (nullable)',
    `submit_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_questionnaire_id` (`questionnaire_id`),
    INDEX `idx_student_user_id` (`student_user_id`),
    INDEX `idx_other_user_id` (`other_user_id`),
    INDEX `idx_submitter_type` (`submitter_type`),
    INDEX `idx_submit_time` (`submit_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='答卷表';

-- 答案详情表
CREATE TABLE IF NOT EXISTS `answer_details` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '答案详情ID',
    `answer_sheet_id` BIGINT NOT NULL COMMENT '所属答卷ID',
    `question_id` BIGINT NOT NULL COMMENT '题目ID',
    `chosen_option_id` BIGINT NOT NULL COMMENT '所选选项ID',
    `is_correct` BOOLEAN NULL COMMENT '是否正确 (nullable)',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_answer_sheet_id` (`answer_sheet_id`),
    INDEX `idx_question_id` (`question_id`),
    INDEX `idx_chosen_option_id` (`chosen_option_id`),
    UNIQUE KEY `uk_answer_question` (`answer_sheet_id`, `question_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='答案详情表';

-- ===========================
-- 初始数据 (Initial Data)
-- ===========================

-- 插入默认管理员账户
INSERT INTO `admin` (`username`, `password`, `role`)
SELECT 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGkqx8zQfbKsLUKoWgKDTbKoO', 'ADMIN'
WHERE NOT EXISTS (
    SELECT 1 FROM `admin` WHERE `username` = 'admin'
);

-- ===========================
-- 恢复外键检查
-- ===========================

SET FOREIGN_KEY_CHECKS = 1;